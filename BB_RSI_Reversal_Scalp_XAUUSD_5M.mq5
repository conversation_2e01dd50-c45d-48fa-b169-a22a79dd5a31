//+------------------------------------------------------------------+
//|                                BB_RSI_Reversal_Scalp_XAUUSD_5M.mq5 |
//|                                                   Trading Strategy |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "BB-RSI Reversal Scalp Strategy"
#property link      ""
#property version   "1.00"
#property description "5-minute XAUUSD BB-RSI Reversal Scalp Strategy"
#property description "Generates 6-8 trades per day with symmetrical long/short signals"

//--- Input parameters
input group "=== Strategy Parameters ==="
input int      InpBBPeriod = 20;           // Bollinger Bands Period
input double   InpBBDeviation = 2.0;       // Bollinger Bands Deviation
input int      InpRSIPeriod = 11;          // RSI Period
input int      InpATRPeriod = 10;          // ATR Period
input int      InpSMA200Period = 200;      // SMA 200 Period
input bool     InpUseSMA200Filter = true;  // Use 200-SMA Trend Filter

input group "=== Entry/Exit Parameters ==="
input double   InpBreakoutPoints = 5.0;    // Breakout Points (above/below signal candle)
input double   InpMinCandleATR = 1.2;      // Minimum Candle Range (ATR multiplier)
input double   InpMaxSpreadPoints = 20.0;  // Maximum Spread (points)

input group "=== Risk Management ==="
input double   InpRiskPercent = 0.4;       // Risk Per Trade (%)
input double   InpSLMultiplier = 1.0;      // Stop Loss (ATR multiplier)
input double   InpTP1Multiplier = 0.8;     // Take Profit 1 (ATR multiplier)
input double   InpTP2Multiplier = 1.6;     // Take Profit 2 (ATR multiplier)
input int      InpTimeStopCandles = 25;    // Time Stop (candles)
input double   InpSMAFlatZone = 5.0;       // SMA Flat Zone ($)

input group "=== Trading Settings ==="
input int      InpMagicNumber = 123456;    // Magic Number
input string   InpTradeComment = "BB-RSI-Scalp"; // Trade Comment

//--- Global variables
int handleBB, handleRSI, handleATR, handleSMA200;
double bbUpper[], bbLower[], bbMiddle[];
double rsiValues[];
double atrValues[];
double sma200Values[];

struct SignalCandle {
   double high;
   double low;
   double close;
   double rsi;
   datetime time;
   bool isValid;
};

SignalCandle lastSignal;
ulong pendingOrderTicket = 0;
datetime entryTime = 0;
bool tp1Hit = false;
double entryPrice = 0;
double currentSL = 0;
double currentTP1 = 0;
double currentTP2 = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   // Initialize indicators
   handleBB = iBands(_Symbol, PERIOD_CURRENT, InpBBPeriod, 0, InpBBDeviation, PRICE_CLOSE);
   handleRSI = iRSI(_Symbol, PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
   handleATR = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
   handleSMA200 = iMA(_Symbol, PERIOD_CURRENT, InpSMA200Period, 0, MODE_SMA, PRICE_CLOSE);
   
   if(handleBB == INVALID_HANDLE || handleRSI == INVALID_HANDLE || 
      handleATR == INVALID_HANDLE || handleSMA200 == INVALID_HANDLE) {
      Print("Error creating indicators");
      return INIT_FAILED;
   }
   
   // Set array properties
   ArraySetAsSeries(bbUpper, true);
   ArraySetAsSeries(bbLower, true);
   ArraySetAsSeries(bbMiddle, true);
   ArraySetAsSeries(rsiValues, true);
   ArraySetAsSeries(atrValues, true);
   ArraySetAsSeries(sma200Values, true);
   
   // Initialize signal structure
   lastSignal.isValid = false;
   
   Print("BB-RSI Reversal Scalp EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // Release indicator handles
   IndicatorRelease(handleBB);
   IndicatorRelease(handleRSI);
   IndicatorRelease(handleATR);
   IndicatorRelease(handleSMA200);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   // Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   bool isNewBar = (currentBarTime != lastBarTime);
   lastBarTime = currentBarTime;
   
   // Update indicator values
   if(!UpdateIndicators()) return;
   
   // Check spread filter
   if(!CheckSpreadFilter()) return;
   
   // Manage existing positions
   ManagePositions();
   
   // Look for new signals only on new bar
   if(isNewBar) {
      CheckForSignals();
   }
   
   // Handle pending orders
   HandlePendingOrders();
}

//+------------------------------------------------------------------+
//| Update all indicator values                                      |
//+------------------------------------------------------------------+
bool UpdateIndicators() {
   if(CopyBuffer(handleBB, 1, 0, 3, bbUpper) <= 0 ||
      CopyBuffer(handleBB, 2, 0, 3, bbLower) <= 0 ||
      CopyBuffer(handleBB, 0, 0, 3, bbMiddle) <= 0 ||
      CopyBuffer(handleRSI, 0, 0, 3, rsiValues) <= 0 ||
      CopyBuffer(handleATR, 0, 0, 3, atrValues) <= 0 ||
      CopyBuffer(handleSMA200, 0, 0, 3, sma200Values) <= 0) {
      return false;
   }
   return true;
}

//+------------------------------------------------------------------+
//| Check spread filter                                              |
//+------------------------------------------------------------------+
bool CheckSpreadFilter() {
   double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
   return (spread <= InpMaxSpreadPoints);
}

//+------------------------------------------------------------------+
//| Check for new trading signals                                    |
//+------------------------------------------------------------------+
void CheckForSignals() {
   // Skip if we already have a position or pending order
   if(PositionsTotal() > 0 || OrdersTotal() > 0) return;
   
   // Get previous candle data (index 1)
   double prevHigh = iHigh(_Symbol, PERIOD_CURRENT, 1);
   double prevLow = iLow(_Symbol, PERIOD_CURRENT, 1);
   double prevClose = iClose(_Symbol, PERIOD_CURRENT, 1);
   double candleRange = prevHigh - prevLow;
   
   // Check candle range filter
   if(candleRange < InpMinCandleATR * atrValues[1]) return;
   
   // Get trend bias
   int trendBias = GetTrendBias();
   
   // Check for LONG signal
   if((trendBias == 1 || trendBias == 0) && 
      prevClose < bbLower[1] && 
      rsiValues[1] < 30) {
      
      lastSignal.high = prevHigh;
      lastSignal.low = prevLow;
      lastSignal.close = prevClose;
      lastSignal.rsi = rsiValues[1];
      lastSignal.time = iTime(_Symbol, PERIOD_CURRENT, 1);
      lastSignal.isValid = true;
      
      PlacePendingOrder(ORDER_TYPE_BUY_STOP, prevHigh + InpBreakoutPoints * _Point);
   }
   
   // Check for SHORT signal
   if((trendBias == -1 || trendBias == 0) && 
      prevClose > bbUpper[1] && 
      rsiValues[1] > 70) {
      
      lastSignal.high = prevHigh;
      lastSignal.low = prevLow;
      lastSignal.close = prevClose;
      lastSignal.rsi = rsiValues[1];
      lastSignal.time = iTime(_Symbol, PERIOD_CURRENT, 1);
      lastSignal.isValid = true;
      
      PlacePendingOrder(ORDER_TYPE_SELL_STOP, prevLow - InpBreakoutPoints * _Point);
   }
}

//+------------------------------------------------------------------+
//| Get trend bias based on 200-SMA                                 |
//+------------------------------------------------------------------+
int GetTrendBias() {
   if(!InpUseSMA200Filter) return 0; // Both directions allowed

   double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double sma200 = sma200Values[0];

   if(currentPrice > sma200 + InpSMAFlatZone) return 1;  // Long only
   if(currentPrice < sma200 - InpSMAFlatZone) return -1; // Short only
   return 0; // Both directions (flat zone)
}

//+------------------------------------------------------------------+
//| Place pending order                                              |
//+------------------------------------------------------------------+
void PlacePendingOrder(ENUM_ORDER_TYPE orderType, double price) {
   double sl, tp1, tp2;
   double atr = atrValues[0];

   // Calculate stop loss and take profits
   if(orderType == ORDER_TYPE_BUY_STOP) {
      sl = price - InpSLMultiplier * atr;
      tp1 = price + InpTP1Multiplier * atr;
      tp2 = price + InpTP2Multiplier * atr;
   } else {
      sl = price + InpSLMultiplier * atr;
      tp1 = price - InpTP1Multiplier * atr;
      tp2 = price - InpTP2Multiplier * atr;
   }

   // Cap stop loss between 15-30 points
   double slDistance = MathAbs(price - sl) / _Point;
   if(slDistance > 30) {
      if(orderType == ORDER_TYPE_BUY_STOP) {
         sl = price - 30 * _Point;
      } else {
         sl = price + 30 * _Point;
      }
   } else if(slDistance < 15) {
      if(orderType == ORDER_TYPE_BUY_STOP) {
         sl = price - 15 * _Point;
      } else {
         sl = price + 15 * _Point;
      }
   }

   // Calculate position size
   double lotSize = CalculatePositionSize(price, sl);
   if(lotSize <= 0) return;

   // Normalize prices
   price = NormalizeDouble(price, _Digits);
   sl = NormalizeDouble(sl, _Digits);
   tp1 = NormalizeDouble(tp1, _Digits);
   tp2 = NormalizeDouble(tp2, _Digits);

   // Place pending order
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_PENDING;
   request.symbol = _Symbol;
   request.volume = lotSize;
   request.type = orderType;
   request.price = price;
   request.sl = sl;
   request.tp = 0; // We'll manage TPs manually
   request.magic = InpMagicNumber;
   request.comment = InpTradeComment;
   request.type_filling = ORDER_FILLING_FOK;

   if(OrderSend(request, result)) {
      pendingOrderTicket = result.order;
      Print("Pending order placed: ", result.order, " at price ", price);
   } else {
      Print("Error placing pending order: ", result.retcode);
   }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk percentage                 |
//+------------------------------------------------------------------+
double CalculatePositionSize(double entryPrice, double stopLoss) {
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpRiskPercent / 100.0;

   double slDistance = MathAbs(entryPrice - stopLoss);
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   double lotSize = riskAmount / (slDistance / tickSize * tickValue);

   // Normalize lot size
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Handle pending orders                                            |
//+------------------------------------------------------------------+
void HandlePendingOrders() {
   if(pendingOrderTicket == 0) return;

   // Check if pending order still exists
   if(!OrderSelect(pendingOrderTicket)) {
      // Order might have been filled or cancelled
      if(PositionsTotal() > 0) {
         // Order was filled, initialize position management
         entryTime = TimeCurrent();
         tp1Hit = false;

         // Get position details
         if(PositionSelect(_Symbol)) {
            entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            currentSL = PositionGetDouble(POSITION_SL);

            double atr = atrValues[0];
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
               currentTP1 = entryPrice + InpTP1Multiplier * atr;
               currentTP2 = entryPrice + InpTP2Multiplier * atr;
            } else {
               currentTP1 = entryPrice - InpTP1Multiplier * atr;
               currentTP2 = entryPrice - InpTP2Multiplier * atr;
            }
         }
      }
      pendingOrderTicket = 0;
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions() {
   if(PositionsTotal() == 0) return;

   if(!PositionSelect(_Symbol)) return;

   double currentPrice = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                        SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                        SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   // Check time stop (25 candles = 125 minutes for 5M timeframe)
   if(TimeCurrent() - entryTime >= InpTimeStopCandles * 5 * 60) {
      ClosePosition("Time Stop");
      return;
   }

   // Check TP1 hit and move SL to breakeven
   if(!tp1Hit) {
      bool tp1Reached = false;
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
         tp1Reached = (currentPrice >= currentTP1);
      } else {
         tp1Reached = (currentPrice <= currentTP1);
      }

      if(tp1Reached) {
         MoveSLToBreakeven();
         tp1Hit = true;
      }
   }

   // Check TP2 hit
   bool tp2Reached = false;
   if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
      tp2Reached = (currentPrice >= currentTP2);
   } else {
      tp2Reached = (currentPrice <= currentTP2);
   }

   if(tp2Reached) {
      ClosePosition("TP2 Hit");
   }
}

//+------------------------------------------------------------------+
//| Move stop loss to breakeven                                     |
//+------------------------------------------------------------------+
void MoveSLToBreakeven() {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.symbol = _Symbol;
   request.sl = NormalizeDouble(entryPrice, _Digits);
   request.tp = PositionGetDouble(POSITION_TP);
   request.magic = InpMagicNumber;

   if(OrderSend(request, result)) {
      currentSL = entryPrice;
      Print("Stop loss moved to breakeven at ", entryPrice);
   }
}

//+------------------------------------------------------------------+
//| Close position                                                   |
//+------------------------------------------------------------------+
void ClosePosition(string reason) {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = PositionGetDouble(POSITION_VOLUME);
   request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                  ORDER_TYPE_SELL : ORDER_TYPE_BUY;
   request.price = (request.type == ORDER_TYPE_SELL) ?
                   SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                   SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   request.magic = InpMagicNumber;
   request.comment = reason;
   request.type_filling = ORDER_FILLING_FOK;

   if(OrderSend(request, result)) {
      Print("Position closed: ", reason);
      ResetTradeVariables();
   }
}

//+------------------------------------------------------------------+
//| Reset trade variables                                            |
//+------------------------------------------------------------------+
void ResetTradeVariables() {
   entryTime = 0;
   tp1Hit = false;
   entryPrice = 0;
   currentSL = 0;
   currentTP1 = 0;
   currentTP2 = 0;
   lastSignal.isValid = false;
}
