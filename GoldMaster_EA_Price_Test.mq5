//+------------------------------------------------------------------+
//|                                   GoldMaster_EA_Price_Test.mq5 |
//|                          Test script for price retrieval fix    |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "GoldMaster EA Price Test"
#property link      ""
#property version   "1.00"
#property script_show_inputs

//--- Include necessary libraries
#include <Trade\SymbolInfo.mqh>

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== GoldMaster EA Price Retrieval Test ===");
    
    CSymbolInfo symbolInfo;
    
    if(!symbolInfo.Name(_Symbol))
    {
        Print("Error: Cannot initialize symbol info for ", _Symbol);
        return;
    }
    
    Print("\n--- Testing Price Retrieval Methods ---");
    
    // Method 1: CSymbolInfo class methods
    Print("Method 1 - CSymbolInfo:");
    Print("  Ask(): ", symbolInfo.Ask());
    Print("  Bid(): ", symbolInfo.Bid());
    
    // Try refreshing rates
    bool refreshResult = symbolInfo.RefreshRates();
    Print("  RefreshRates(): ", (refreshResult ? "Success" : "Failed"));
    Print("  Ask() after refresh: ", symbolInfo.Ask());
    Print("  Bid() after refresh: ", symbolInfo.Bid());
    
    // Method 2: Direct SymbolInfo functions
    Print("\nMethod 2 - Direct SymbolInfo:");
    double askDirect = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bidDirect = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    Print("  SYMBOL_ASK: ", askDirect);
    Print("  SYMBOL_BID: ", bidDirect);
    
    // Method 3: Current bar close price
    Print("\nMethod 3 - Current Bar:");
    double currentClose = iClose(_Symbol, PERIOD_H1, 0);
    double currentHigh = iHigh(_Symbol, PERIOD_H1, 0);
    double currentLow = iLow(_Symbol, PERIOD_H1, 0);
    Print("  Close[0]: ", currentClose);
    Print("  High[0]: ", currentHigh);
    Print("  Low[0]: ", currentLow);
    
    // Method 4: Market info
    Print("\nMethod 4 - Market Info:");
    double spread = symbolInfo.Spread() * symbolInfo.Point();
    Print("  Spread: ", spread);
    Print("  Point: ", symbolInfo.Point());
    Print("  Digits: ", symbolInfo.Digits());
    
    // Test the fallback logic
    Print("\n--- Testing Fallback Logic ---");
    TestPriceRetrievalLogic(ORDER_TYPE_BUY, symbolInfo);
    TestPriceRetrievalLogic(ORDER_TYPE_SELL, symbolInfo);
    
    // Test during different market conditions
    Print("\n--- Market Condition Analysis ---");
    AnalyzeMarketConditions(symbolInfo);
    
    Print("\n=== Price Retrieval Test Completed ===");
}

//+------------------------------------------------------------------+
//| Test the price retrieval logic used in the EA                   |
//+------------------------------------------------------------------+
void TestPriceRetrievalLogic(ENUM_ORDER_TYPE orderType, CSymbolInfo &symbolInfo)
{
    Print("\nTesting price retrieval for ", EnumToString(orderType), ":");
    
    double price = 0;
    string method = "";
    
    if(orderType == ORDER_TYPE_BUY)
    {
        price = symbolInfo.Ask();
        method = "symbolInfo.Ask()";
        
        if(price <= 0)
        {
            price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            method = "SymbolInfoDouble(SYMBOL_ASK)";
            
            if(price <= 0)
            {
                price = iClose(_Symbol, PERIOD_H1, 0);
                double spread = symbolInfo.Spread() * symbolInfo.Point();
                price += spread;
                method = "iClose() + spread";
            }
        }
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = symbolInfo.Bid();
        method = "symbolInfo.Bid()";
        
        if(price <= 0)
        {
            price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            method = "SymbolInfoDouble(SYMBOL_BID)";
            
            if(price <= 0)
            {
                price = iClose(_Symbol, PERIOD_H1, 0);
                method = "iClose()";
            }
        }
    }
    
    Print("  Final price: ", price);
    Print("  Method used: ", method);
    Print("  Valid: ", (price > 0 ? "YES" : "NO"));
    
    if(price > 0)
    {
        // Test stop loss calculation with this price
        double point = symbolInfo.Point();
        double pipValue = point * 10;
        double stopLoss = 0;
        
        if(orderType == ORDER_TYPE_BUY)
            stopLoss = price - (400 * pipValue);
        else
            stopLoss = price + (400 * pipValue);
        
        Print("  Test SL: ", stopLoss, " (Distance: ", MathAbs(price - stopLoss), ")");
    }
}

//+------------------------------------------------------------------+
//| Analyze current market conditions                               |
//+------------------------------------------------------------------+
void AnalyzeMarketConditions(CSymbolInfo &symbolInfo)
{
    // Check if market is open
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);
    
    Print("Current time: ", TimeToString(TimeCurrent()));
    Print("Day of week: ", currentTime.day_of_week, " (0=Sunday, 6=Saturday)");
    Print("Hour: ", currentTime.hour);
    
    // Check symbol trading status
    bool isTradeAllowed = symbolInfo.TradeMode() == SYMBOL_TRADE_MODE_FULL;
    Print("Trade allowed: ", (isTradeAllowed ? "YES" : "NO"));
    Print("Trade mode: ", symbolInfo.TradeMode());
    
    // Check session times
    MqlDateTime sessionFrom, sessionTo;
    if(SymbolInfoSessionTrade(_Symbol, currentTime.day_of_week, 0, sessionFrom, sessionTo))
    {
        Print("Trading session: ", sessionFrom.hour, ":", sessionFrom.min, " - ", sessionTo.hour, ":", sessionTo.min);
    }
    else
    {
        Print("Could not get trading session info");
    }
    
    // Check last tick time
    MqlTick lastTick;
    if(SymbolInfoTick(_Symbol, lastTick))
    {
        Print("Last tick time: ", TimeToString(lastTick.time));
        Print("Last tick bid: ", lastTick.bid);
        Print("Last tick ask: ", lastTick.ask);
        
        // Check if tick is recent (within last minute)
        datetime timeDiff = TimeCurrent() - lastTick.time;
        Print("Tick age: ", timeDiff, " seconds");
        
        if(timeDiff > 60)
        {
            Print("WARNING: Last tick is old, market may be closed");
        }
    }
    else
    {
        Print("Could not get last tick info");
    }
    
    // Check bars availability
    int bars = iBars(_Symbol, PERIOD_H1);
    Print("Available H1 bars: ", bars);
    
    if(bars < 200)
    {
        Print("WARNING: Insufficient bars for indicator calculations");
    }
    
    // Test indicator creation
    int testMA = iMA(_Symbol, PERIOD_H1, 50, 0, MODE_EMA, PRICE_CLOSE);
    if(testMA == INVALID_HANDLE)
    {
        Print("ERROR: Cannot create MA indicator");
    }
    else
    {
        Print("MA indicator created successfully");
        IndicatorRelease(testMA);
    }
}

//+------------------------------------------------------------------+
//| Test complete trade scenario with current market data           |
//+------------------------------------------------------------------+
void TestCompleteTradeScenario()
{
    Print("\n--- Complete Trade Scenario Test ---");
    
    CSymbolInfo symbolInfo;
    if(!symbolInfo.Name(_Symbol))
    {
        Print("Cannot test - symbol info failed");
        return;
    }
    
    // Refresh rates
    symbolInfo.RefreshRates();
    
    // Get price using fallback logic
    double buyPrice = symbolInfo.Ask();
    if(buyPrice <= 0)
    {
        buyPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        if(buyPrice <= 0)
        {
            buyPrice = iClose(_Symbol, PERIOD_H1, 0);
            double spread = symbolInfo.Spread() * symbolInfo.Point();
            buyPrice += spread;
        }
    }
    
    if(buyPrice > 0)
    {
        Print("✓ Price retrieval: SUCCESS (", buyPrice, ")");
        
        // Test stop calculations
        double point = symbolInfo.Point();
        double pipValue = point * 10;
        double stopLoss = buyPrice - (400 * pipValue);
        double takeProfit = buyPrice + (1200 * pipValue);
        
        Print("✓ Stop calculations: SL=", stopLoss, " TP=", takeProfit);
        
        // Validate stops
        double minStopLevel = symbolInfo.StopsLevel() * symbolInfo.Point();
        bool valid = (buyPrice - stopLoss >= minStopLevel) && (takeProfit - buyPrice >= minStopLevel);
        
        Print("✓ Stop validation: ", (valid ? "PASSED" : "FAILED"));
        
        if(valid)
        {
            Print("\n🎉 COMPLETE SCENARIO: READY FOR TRADING");
        }
        else
        {
            Print("\n⚠️ COMPLETE SCENARIO: STOPS NEED ADJUSTMENT");
        }
    }
    else
    {
        Print("✗ Price retrieval: FAILED");
        Print("\n❌ COMPLETE SCENARIO: CANNOT TRADE WITHOUT VALID PRICES");
    }
}
