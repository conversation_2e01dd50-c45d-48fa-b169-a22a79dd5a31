# GoldMaster EA - Comprehensive Gold Trading Expert Advisor

## Overview

GoldMaster EA is a sophisticated, research-based Expert Advisor specifically designed for trading Gold (XAU/USD) on the MetaTrader 5 platform. This EA combines multiple proven trading strategies identified through extensive research of successful gold trading systems, academic papers, and community-validated approaches.

## Key Features

### Multi-Strategy Approach
- **Open Range Breakout (ORB)**: Based on the proven GOLD_ORB strategy
- **Trend Following**: Using EMA crossovers with RSI confirmation
- **Mean Reversion**: RSI-based oversold/overbought signals

### Advanced Risk Management
- Dynamic position sizing based on account equity
- Maximum drawdown protection (default 10%)
- Risk per trade percentage control (default 1%)
- Intelligent stop loss and take profit calculation
- Trailing stop functionality
- Daily trade limits

### Gold-Specific Optimizations
- Optimized for XAU/USD volatility characteristics
- Market hours awareness (London/New York overlap)
- Spread-adjusted profit targets
- Volatility-based position sizing

## Strategy Details

### 1. Open Range Breakout (ORB)
**Logic**: 
- Identifies the opening range (high/low) in the first hour after market opens
- Waits for minimum consolidation period (default 3 candles)
- Generates buy signal on breakout above range
- Generates sell signal on breakdown below range

**Advantages**:
- Captures early momentum moves
- Well-suited for gold's volatility
- Clear entry and exit rules

### 2. Trend Following
**Logic**:
- Uses 50 EMA and 200 EMA crossovers
- Requires RSI confirmation (not overbought/oversold)
- Enters on momentum continuation

**Advantages**:
- Catches major trend moves
- Filters false signals with RSI
- Good for trending markets

### 3. Mean Reversion
**Logic**:
- Identifies RSI extremes (>70 overbought, <30 oversold)
- Enters counter-trend positions
- Suitable for ranging markets

**Advantages**:
- Profits from market corrections
- Good risk-reward ratios
- Complements trend following

## Input Parameters

### Trading Strategy Settings
- `EnableORB`: Enable/disable Open Range Breakout strategy
- `EnableTrendFollowing`: Enable/disable Trend Following strategy
- `EnableMeanReversion`: Enable/disable Mean Reversion strategy
- `ORB_Period`: Minimum candles for ORB range confirmation (default: 3)
- `MA_Fast`: Fast moving average period (default: 50)
- `MA_Slow`: Slow moving average period (default: 200)
- `RSI_Period`: RSI calculation period (default: 14)
- `RSI_Overbought`: RSI overbought level (default: 70)
- `RSI_Oversold`: RSI oversold level (default: 30)

### Risk Management Settings
- `RiskPercentage`: Risk per trade as % of account (default: 1.0%)
- `MaxDrawdownPercent`: Maximum allowed drawdown (default: 10.0%)
- `StopLossPips`: Stop loss in pips (default: 400)
- `TakeProfitPips`: Take profit in pips (default: 1200)
- `RiskRewardRatio`: Risk to reward ratio (default: 3.0)
- `UseTrailingStop`: Enable trailing stop (default: true)
- `TrailingStopPips`: Trailing stop distance (default: 100)
- `TrailingStepPips`: Trailing step size (default: 50)

### Trade Management Settings
- `MaxTradesPerDay`: Maximum trades per day (default: 2)
- `AllowLongTrades`: Allow buy positions (default: true)
- `AllowShortTrades`: Allow sell positions (default: true)
- `MagicNumber`: Unique identifier for EA trades (default: 123456)
- `TradeComment`: Comment for trades (default: "GoldMaster_EA")

### Time Filter Settings
- `StartTradingHour`: Start trading hour server time (default: 1)
- `StopTradingHour`: Stop trading hour server time (default: 23)
- `TradeOnMonday`: Allow trading on Monday (default: true)
- `TradeOnFriday`: Allow trading on Friday (default: true)

## Installation Instructions

### Prerequisites
- MetaTrader 5 platform
- Gold (XAU/USD) trading account
- Minimum account balance: $1,000 (recommended: $5,000+)
- Stable internet connection

### Installation Steps

1. **Download Files**
   - `GoldMaster_EA.mq5` (main EA file)
   - `GoldMaster_EA_Test.mq5` (test script)
   - `GoldMaster_EA_Documentation.md` (this file)

2. **Install in MetaTrader 5**
   - Open MetaTrader 5
   - Press `Ctrl+Shift+D` to open Data Folder
   - Navigate to `MQL5\Experts\`
   - Copy `GoldMaster_EA.mq5` to this folder
   - Copy `GoldMaster_EA_Test.mq5` to `MQL5\Scripts\`
   - Restart MetaTrader 5

3. **Compile the EA**
   - Open MetaEditor (F4 in MT5)
   - Open `GoldMaster_EA.mq5`
   - Press F7 to compile
   - Ensure no errors in compilation

4. **Run Test Script**
   - Attach `GoldMaster_EA_Test.mq5` to any chart
   - Check that all tests pass
   - Review any warnings

5. **Attach EA to Chart**
   - Open XAU/USD H1 chart
   - Drag `GoldMaster_EA` from Navigator to chart
   - Configure input parameters
   - Enable AutoTrading (Ctrl+E)
   - Click OK

## Recommended Settings

### Conservative Settings (Low Risk)
- Risk Percentage: 0.5%
- Max Drawdown: 5%
- Enable only ORB strategy
- Max Trades Per Day: 1

### Moderate Settings (Balanced)
- Risk Percentage: 1.0%
- Max Drawdown: 10%
- Enable ORB + Trend Following
- Max Trades Per Day: 2

### Aggressive Settings (High Risk)
- Risk Percentage: 2.0%
- Max Drawdown: 15%
- Enable all strategies
- Max Trades Per Day: 3

## Broker Requirements

### Recommended Broker Features
- Low spreads on XAU/USD (< 3 pips average)
- Fast execution (< 100ms)
- No requotes
- Hedging allowed
- Minimum lot size: 0.01
- Maximum leverage: 1:100 or higher

### Account Requirements
- Account type: Standard or ECN
- Base currency: USD (recommended)
- Minimum balance: $1,000
- Recommended balance: $5,000+

## Performance Monitoring

### Key Metrics to Monitor
- Daily/Weekly P&L
- Maximum drawdown
- Win rate
- Average risk-reward ratio
- Number of trades per day
- Slippage and spread costs

### Warning Signs
- Drawdown exceeding 15%
- Win rate below 40%
- Excessive slippage (>2 pips average)
- Frequent requotes
- EA stops trading unexpectedly

## Troubleshooting

### Common Issues

**EA Not Trading**
- Check AutoTrading is enabled
- Verify account has sufficient margin
- Check time filters
- Ensure market is open
- Review daily trade limits

**Poor Performance**
- Check spread conditions
- Verify broker execution quality
- Review parameter settings
- Consider market conditions
- Check for news events

**Compilation Errors**
- Ensure MT5 is updated
- Check MQL5 syntax
- Verify all includes are available
- Review error messages carefully

## Risk Disclaimer

**IMPORTANT**: Trading forex and CFDs involves significant risk and may not be suitable for all investors. The high degree of leverage can work against you as well as for you. Before deciding to trade forex/CFDs, you should carefully consider your investment objectives, level of experience, and risk appetite.

**Key Risks**:
- Past performance does not guarantee future results
- Automated trading systems can fail
- Market conditions can change rapidly
- Technical failures can occur
- Slippage and spread costs affect profitability

**Recommendations**:
- Start with demo account testing
- Use only risk capital you can afford to lose
- Monitor EA performance regularly
- Keep detailed trading records
- Consider professional advice

## Support and Updates

### Getting Help
- Review documentation thoroughly
- Test on demo account first
- Check MT5 logs for errors
- Verify broker compatibility
- Consider professional consultation

### Version History
- v1.00: Initial release with multi-strategy approach

## Quick Start Guide

### Step-by-Step Setup (5 Minutes)

1. **Prepare MetaTrader 5**
   ```
   - Open MT5
   - Ensure XAU/USD symbol is available
   - Switch to H1 timeframe
   - Enable AutoTrading (Ctrl+E)
   ```

2. **Install EA**
   ```
   - Copy GoldMaster_EA.mq5 to MQL5\Experts\
   - Compile in MetaEditor (F7)
   - Refresh Navigator (F5)
   ```

3. **Run Tests**
   ```
   - Copy GoldMaster_EA_Test.mq5 to MQL5\Scripts\
   - Run test script on any chart
   - Verify all tests pass
   ```

4. **Configure EA**
   ```
   - Drag EA to XAU/USD H1 chart
   - Set Risk Percentage: 1.0%
   - Set Max Drawdown: 10.0%
   - Enable desired strategies
   - Click OK
   ```

5. **Monitor Performance**
   ```
   - Check chart comment for status
   - Monitor account equity
   - Review trade history
   - Adjust settings if needed
   ```

### Backtesting Instructions

1. **Open Strategy Tester**
   - Press Ctrl+R in MT5
   - Select GoldMaster_EA
   - Choose XAU/USD symbol
   - Set H1 timeframe

2. **Configure Test Parameters**
   - Period: Last 6 months minimum
   - Model: Every tick (most accurate)
   - Optimization: Disabled for initial test
   - Visual mode: Optional

3. **Set EA Parameters**
   - Use conservative settings first
   - Enable only one strategy initially
   - Set realistic spread (2-3 pips)

4. **Analyze Results**
   - Profit factor should be > 1.3
   - Maximum drawdown < 15%
   - Win rate > 45%
   - Sharpe ratio > 1.0

### Optimization Guide

**Parameter Optimization Priority**:
1. Risk Percentage (0.5% - 2.0%)
2. Stop Loss Pips (300 - 500)
3. Take Profit Pips (900 - 1500)
4. ORB Period (2 - 5)
5. MA periods (adjust based on market conditions)

**Optimization Tips**:
- Test one parameter at a time
- Use walk-forward analysis
- Consider different market conditions
- Validate on out-of-sample data

## Advanced Features

### Custom Modifications

The EA is designed for easy customization:

```mql5
// Add custom indicator
int customHandle = iCustom(_Symbol, PERIOD_H1, "CustomIndicator");

// Add custom signal logic
int CheckCustomSignal()
{
    // Your custom logic here
    return 0; // 1 for buy, -1 for sell, 0 for no signal
}

// Integrate in CheckTradingSignals()
if(EnableCustomStrategy)
{
    int customSignal = CheckCustomSignal();
    // Process signal...
}
```

### Integration with Other Systems

- **Trade Copier**: Use MagicNumber to identify EA trades
- **Risk Manager**: Monitor via trade comments
- **Telegram Notifications**: Add webhook calls
- **Database Logging**: Store trade data for analysis

---

**Developed by**: Research-Based Trading Systems
**Last Updated**: January 2025
**Compatible with**: MetaTrader 5 Build 3815+
**Optimized for**: XAU/USD (Gold) trading
**License**: Educational and Research Use
