//+------------------------------------------------------------------+
//|                                        GoldMaster_EA_Test.mq5 |
//|                                Test Script for GoldMaster EA |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "GoldMaster EA Test Suite"
#property link      ""
#property version   "1.00"
#property script_show_inputs

//--- Input parameters for testing
input group "=== TEST PARAMETERS ==="
input bool TestPositionSizing = true;          // Test position sizing calculations
input bool TestRiskManagement = true;          // Test risk management functions
input bool TestIndicatorValues = true;         // Test indicator calculations
input bool TestTimeFilters = true;             // Test time filter functions
input double TestAccountBalance = 10000.0;     // Test account balance
input double TestRiskPercent = 1.0;            // Test risk percentage
input int TestStopLossPips = 400;              // Test stop loss in pips

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== GoldMaster EA Test Suite Started ===");
    
    bool allTestsPassed = true;
    
    if(TestPositionSizing)
    {
        Print("\n--- Testing Position Sizing ---");
        if(!TestPositionSizingFunction())
            allTestsPassed = false;
    }
    
    if(TestRiskManagement)
    {
        Print("\n--- Testing Risk Management ---");
        if(!TestRiskManagementFunction())
            allTestsPassed = false;
    }
    
    if(TestIndicatorValues)
    {
        Print("\n--- Testing Indicator Values ---");
        if(!TestIndicatorFunction())
            allTestsPassed = false;
    }
    
    if(TestTimeFilters)
    {
        Print("\n--- Testing Time Filters ---");
        if(!TestTimeFilterFunction())
            allTestsPassed = false;
    }
    
    Print("\n=== Test Suite Results ===");
    if(allTestsPassed)
    {
        Print("✓ ALL TESTS PASSED - GoldMaster EA is ready for deployment");
    }
    else
    {
        Print("✗ SOME TESTS FAILED - Please review the code before deployment");
    }
    
    Print("=== GoldMaster EA Test Suite Completed ===");
}

//+------------------------------------------------------------------+
//| Test position sizing calculations                                |
//+------------------------------------------------------------------+
bool TestPositionSizingFunction()
{
    bool testPassed = true;
    
    // Test 1: Basic position sizing calculation
    double riskAmount = TestAccountBalance * (TestRiskPercent / 100.0);
    double expectedRiskAmount = 100.0; // 1% of 10000
    
    if(MathAbs(riskAmount - expectedRiskAmount) > 0.01)
    {
        Print("✗ Position Sizing Test 1 FAILED: Risk amount calculation");
        Print("  Expected: ", expectedRiskAmount, " Got: ", riskAmount);
        testPassed = false;
    }
    else
    {
        Print("✓ Position Sizing Test 1 PASSED: Risk amount = ", riskAmount);
    }
    
    // Test 2: Lot size calculation logic
    CSymbolInfo symbolInfo;
    if(symbolInfo.Name(_Symbol))
    {
        double pipValue = symbolInfo.TickValue();
        if(symbolInfo.Digits() == 5 || symbolInfo.Digits() == 3)
            pipValue *= 10;
        
        double lotSize = riskAmount / (TestStopLossPips * pipValue);
        
        if(lotSize > 0 && lotSize <= symbolInfo.LotsMax())
        {
            Print("✓ Position Sizing Test 2 PASSED: Lot size = ", lotSize);
        }
        else
        {
            Print("✗ Position Sizing Test 2 FAILED: Invalid lot size = ", lotSize);
            testPassed = false;
        }
    }
    else
    {
        Print("✗ Position Sizing Test 2 FAILED: Cannot initialize symbol info");
        testPassed = false;
    }
    
    return testPassed;
}

//+------------------------------------------------------------------+
//| Test risk management functions                                   |
//+------------------------------------------------------------------+
bool TestRiskManagementFunction()
{
    bool testPassed = true;
    
    // Test 1: Drawdown calculation
    double startBalance = 10000.0;
    double currentBalance = 9000.0;
    double expectedDrawdown = 10.0; // 10%
    
    double actualDrawdown = ((startBalance - currentBalance) / startBalance) * 100;
    
    if(MathAbs(actualDrawdown - expectedDrawdown) > 0.01)
    {
        Print("✗ Risk Management Test 1 FAILED: Drawdown calculation");
        Print("  Expected: ", expectedDrawdown, "% Got: ", actualDrawdown, "%");
        testPassed = false;
    }
    else
    {
        Print("✓ Risk Management Test 1 PASSED: Drawdown = ", actualDrawdown, "%");
    }
    
    // Test 2: Stop loss and take profit calculation
    double entryPrice = 2000.0;
    double stopLossPips = 400;
    double riskRewardRatio = 3.0;
    
    double point = 0.01; // Assuming 5-digit broker
    double stopLoss = entryPrice - (stopLossPips * point);
    double stopLossDistance = entryPrice - stopLoss;
    double takeProfit = entryPrice + (stopLossDistance * riskRewardRatio);
    
    double expectedStopLoss = 1996.0;
    double expectedTakeProfit = 2012.0;
    
    if(MathAbs(stopLoss - expectedStopLoss) > 0.01 || MathAbs(takeProfit - expectedTakeProfit) > 0.01)
    {
        Print("✗ Risk Management Test 2 FAILED: SL/TP calculation");
        Print("  Expected SL: ", expectedStopLoss, " Got: ", stopLoss);
        Print("  Expected TP: ", expectedTakeProfit, " Got: ", takeProfit);
        testPassed = false;
    }
    else
    {
        Print("✓ Risk Management Test 2 PASSED: SL = ", stopLoss, " TP = ", takeProfit);
    }
    
    return testPassed;
}

//+------------------------------------------------------------------+
//| Test indicator functions                                         |
//+------------------------------------------------------------------+
bool TestIndicatorFunction()
{
    bool testPassed = true;
    
    // Test 1: Create indicator handles
    int handleMA = iMA(_Symbol, PERIOD_H1, 50, 0, MODE_EMA, PRICE_CLOSE);
    int handleRSI = iRSI(_Symbol, PERIOD_H1, 14, PRICE_CLOSE);
    
    if(handleMA == INVALID_HANDLE)
    {
        Print("✗ Indicator Test 1 FAILED: Cannot create MA handle");
        testPassed = false;
    }
    else
    {
        Print("✓ Indicator Test 1 PASSED: MA handle created successfully");
        IndicatorRelease(handleMA);
    }
    
    if(handleRSI == INVALID_HANDLE)
    {
        Print("✗ Indicator Test 2 FAILED: Cannot create RSI handle");
        testPassed = false;
    }
    else
    {
        Print("✓ Indicator Test 2 PASSED: RSI handle created successfully");
        IndicatorRelease(handleRSI);
    }
    
    // Test 2: Check if we have enough bars for calculations
    int bars = iBars(_Symbol, PERIOD_H1);
    if(bars < 200)
    {
        Print("⚠ Indicator Test 3 WARNING: Only ", bars, " bars available (need 200+ for reliable signals)");
    }
    else
    {
        Print("✓ Indicator Test 3 PASSED: Sufficient bars available (", bars, ")");
    }
    
    return testPassed;
}

//+------------------------------------------------------------------+
//| Test time filter functions                                       |
//+------------------------------------------------------------------+
bool TestTimeFilterFunction()
{
    bool testPassed = true;
    
    // Test 1: Current time structure
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);
    
    if(currentTime.year < 2020 || currentTime.year > 2030)
    {
        Print("✗ Time Filter Test 1 FAILED: Invalid year = ", currentTime.year);
        testPassed = false;
    }
    else
    {
        Print("✓ Time Filter Test 1 PASSED: Current time = ", TimeToString(TimeCurrent()));
    }
    
    // Test 2: Day of week validation
    if(currentTime.day_of_week < 0 || currentTime.day_of_week > 6)
    {
        Print("✗ Time Filter Test 2 FAILED: Invalid day of week = ", currentTime.day_of_week);
        testPassed = false;
    }
    else
    {
        string dayNames[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
        Print("✓ Time Filter Test 2 PASSED: Day of week = ", dayNames[currentTime.day_of_week]);
    }
    
    // Test 3: Trading hours validation
    bool validTradingHours = (currentTime.hour >= 0 && currentTime.hour <= 23);
    if(!validTradingHours)
    {
        Print("✗ Time Filter Test 3 FAILED: Invalid hour = ", currentTime.hour);
        testPassed = false;
    }
    else
    {
        Print("✓ Time Filter Test 3 PASSED: Current hour = ", currentTime.hour);
    }
    
    return testPassed;
}

//+------------------------------------------------------------------+
//| Additional validation functions                                  |
//+------------------------------------------------------------------+
void ValidateEASettings()
{
    Print("\n=== EA Settings Validation ===");
    
    // Validate symbol
    if(_Symbol != "XAUUSD" && StringFind(_Symbol, "XAU") == -1 && StringFind(_Symbol, "GOLD") == -1)
    {
        Print("⚠ WARNING: EA optimized for Gold (XAU/USD), current symbol: ", _Symbol);
    }
    
    // Validate timeframe
    if(_Period != PERIOD_H1)
    {
        Print("⚠ WARNING: EA optimized for H1 timeframe, current: ", EnumToString(_Period));
    }
    
    // Validate broker settings
    CSymbolInfo symbolInfo;
    if(symbolInfo.Name(_Symbol))
    {
        Print("Symbol: ", _Symbol);
        Print("Digits: ", symbolInfo.Digits());
        Print("Point: ", symbolInfo.Point());
        Print("Tick Size: ", symbolInfo.TickSize());
        Print("Tick Value: ", symbolInfo.TickValue());
        Print("Min Lot: ", symbolInfo.LotsMin());
        Print("Max Lot: ", symbolInfo.LotsMax());
        Print("Lot Step: ", symbolInfo.LotsStep());
        Print("Spread: ", symbolInfo.Spread(), " points");
    }
}
