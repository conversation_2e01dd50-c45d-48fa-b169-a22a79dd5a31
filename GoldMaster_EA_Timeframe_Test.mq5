//+------------------------------------------------------------------+
//|                              GoldMaster_EA_Timeframe_Test.mq5 |
//|                    Test script to demonstrate timeframe impact  |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "GoldMaster EA Timeframe Test"
#property link      ""
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input int TestPeriodBars = 100;        // Number of bars to analyze

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== GoldMaster EA Timeframe Impact Test ===");
    
    // Test different timeframes
    ENUM_TIMEFRAMES timeframes[] = {
        PERIOD_M5,   // 5 minutes
        PERIOD_M15,  // 15 minutes
        PERIOD_M30,  // 30 minutes
        PERIOD_H1,   // 1 hour
        PERIOD_H4,   // 4 hours
        PERIOD_D1    // Daily
    };
    
    Print("\nAnalyzing signal frequency across different timeframes...\n");
    
    for(int i = 0; i < ArraySize(timeframes); i++)
    {
        AnalyzeTimeframe(timeframes[i]);
    }
    
    Print("\n=== Analysis Complete ===");
    Print("\nKey Findings:");
    Print("- Lower timeframes (M5, M15) should show MORE frequent signals");
    Print("- Higher timeframes (H4, D1) should show FEWER but stronger signals");
    Print("- ORB strategy works best on H1 timeframe");
    Print("- Trend following adapts to any timeframe");
    
    Print("\nRecommendations:");
    Print("- M5/M15: Good for scalping, use tight stops");
    Print("- H1: Optimal for ORB strategy, balanced approach");
    Print("- H4/D1: Good for swing trading, wider stops");
}

//+------------------------------------------------------------------+
//| Analyze signals on specific timeframe                           |
//+------------------------------------------------------------------+
void AnalyzeTimeframe(ENUM_TIMEFRAMES timeframe)
{
    Print("--- ", EnumToString(timeframe), " Analysis ---");
    
    // Create indicators for this timeframe
    int handleMA_Fast = iMA(_Symbol, timeframe, 50, 0, MODE_EMA, PRICE_CLOSE);
    int handleMA_Slow = iMA(_Symbol, timeframe, 200, 0, MODE_EMA, PRICE_CLOSE);
    int handleRSI = iRSI(_Symbol, timeframe, 14, PRICE_CLOSE);
    
    if(handleMA_Fast == INVALID_HANDLE || handleMA_Slow == INVALID_HANDLE || handleRSI == INVALID_HANDLE)
    {
        Print("Error creating indicators for ", EnumToString(timeframe));
        return;
    }
    
    // Wait for indicators to calculate
    Sleep(100);
    
    // Get indicator data
    double maFast[], maSlow[], rsi[];
    ArraySetAsSeries(maFast, true);
    ArraySetAsSeries(maSlow, true);
    ArraySetAsSeries(rsi, true);
    
    int barsToAnalyze = MathMin(TestPeriodBars, iBars(_Symbol, timeframe) - 200);
    
    if(CopyBuffer(handleMA_Fast, 0, 0, barsToAnalyze, maFast) < barsToAnalyze ||
       CopyBuffer(handleMA_Slow, 0, 0, barsToAnalyze, maSlow) < barsToAnalyze ||
       CopyBuffer(handleRSI, 0, 0, barsToAnalyze, rsi) < barsToAnalyze)
    {
        Print("Error copying indicator data for ", EnumToString(timeframe));
        IndicatorRelease(handleMA_Fast);
        IndicatorRelease(handleMA_Slow);
        IndicatorRelease(handleRSI);
        return;
    }
    
    // Count signals
    int trendFollowingSignals = 0;
    int meanReversionSignals = 0;
    int orbSignals = 0;
    
    // Analyze trend following signals
    for(int i = 1; i < barsToAnalyze - 1; i++)
    {
        // MA crossover signals
        bool bullishCrossover = maFast[i+1] <= maSlow[i+1] && maFast[i] > maSlow[i];
        bool bearishCrossover = maFast[i+1] >= maSlow[i+1] && maFast[i] < maSlow[i];
        
        // RSI confirmation
        bool rsiNotOverbought = rsi[i] < 70;
        bool rsiNotOversold = rsi[i] > 30;
        
        if((bullishCrossover && rsiNotOverbought) || (bearishCrossover && rsiNotOversold))
        {
            trendFollowingSignals++;
        }
    }
    
    // Analyze mean reversion signals
    for(int i = 1; i < barsToAnalyze - 1; i++)
    {
        if((rsi[i] < 30 && rsi[i+1] >= 30) || (rsi[i] > 70 && rsi[i+1] <= 70))
        {
            meanReversionSignals++;
        }
    }
    
    // Analyze ORB signals (simplified)
    if(timeframe == PERIOD_H1)
    {
        // ORB works best on H1
        orbSignals = EstimateORBSignals(timeframe, barsToAnalyze);
    }
    else
    {
        // Estimate ORB adaptation for other timeframes
        orbSignals = EstimateORBSignals(timeframe, barsToAnalyze);
    }
    
    // Calculate signal frequency
    double trendFrequency = (double)trendFollowingSignals / barsToAnalyze * 100;
    double meanRevFrequency = (double)meanReversionSignals / barsToAnalyze * 100;
    double orbFrequency = (double)orbSignals / barsToAnalyze * 100;
    
    // Display results
    Print("Bars analyzed: ", barsToAnalyze);
    Print("Trend Following signals: ", trendFollowingSignals, " (", DoubleToString(trendFrequency, 2), "%)");
    Print("Mean Reversion signals: ", meanReversionSignals, " (", DoubleToString(meanRevFrequency, 2), "%)");
    Print("ORB signals (estimated): ", orbSignals, " (", DoubleToString(orbFrequency, 2), "%)");
    
    // Timeframe-specific recommendations
    string recommendation = GetTimeframeRecommendation(timeframe, trendFrequency, meanRevFrequency);
    Print("Recommendation: ", recommendation);
    Print("");
    
    // Release indicators
    IndicatorRelease(handleMA_Fast);
    IndicatorRelease(handleMA_Slow);
    IndicatorRelease(handleRSI);
}

//+------------------------------------------------------------------+
//| Estimate ORB signals for different timeframes                   |
//+------------------------------------------------------------------+
int EstimateORBSignals(ENUM_TIMEFRAMES timeframe, int bars)
{
    // ORB signals depend on volatility and timeframe
    // Lower timeframes = more frequent but smaller breakouts
    // Higher timeframes = fewer but larger breakouts
    
    double baseSignals = bars * 0.02; // Base 2% signal rate
    
    switch(timeframe)
    {
        case PERIOD_M5:  return (int)(baseSignals * 3.0);  // More frequent on M5
        case PERIOD_M15: return (int)(baseSignals * 2.0);  // Frequent on M15
        case PERIOD_M30: return (int)(baseSignals * 1.5);  // Moderate on M30
        case PERIOD_H1:  return (int)(baseSignals * 1.0);  // Optimal on H1
        case PERIOD_H4:  return (int)(baseSignals * 0.5);  // Less frequent on H4
        case PERIOD_D1:  return (int)(baseSignals * 0.2);  // Rare on D1
        default:         return (int)(baseSignals);
    }
}

//+------------------------------------------------------------------+
//| Get timeframe-specific recommendations                           |
//+------------------------------------------------------------------+
string GetTimeframeRecommendation(ENUM_TIMEFRAMES timeframe, double trendFreq, double meanRevFreq)
{
    string rec = "";
    
    switch(timeframe)
    {
        case PERIOD_M5:
            rec = "High frequency scalping. Use tight stops (50-100 pips). Monitor spreads.";
            break;
        case PERIOD_M15:
            rec = "Short-term trading. Good for intraday strategies. Stops: 100-200 pips.";
            break;
        case PERIOD_M30:
            rec = "Balanced approach. Mix of scalping and swing. Stops: 200-300 pips.";
            break;
        case PERIOD_H1:
            rec = "OPTIMAL for ORB strategy. Best balance of signals and reliability.";
            break;
        case PERIOD_H4:
            rec = "Swing trading. Fewer but stronger signals. Stops: 400-600 pips.";
            break;
        case PERIOD_D1:
            rec = "Long-term positions. Weekly holds. Stops: 800+ pips.";
            break;
        default:
            rec = "Custom timeframe. Adjust parameters accordingly.";
    }
    
    return rec;
}

//+------------------------------------------------------------------+
//| Compare current chart timeframe with H1                         |
//+------------------------------------------------------------------+
void CompareWithH1()
{
    Print("\n--- Current Chart vs H1 Comparison ---");
    
    ENUM_TIMEFRAMES currentTF = _Period;
    Print("Current chart timeframe: ", EnumToString(currentTF));
    
    if(currentTF == PERIOD_H1)
    {
        Print("✓ Perfect! You're using the optimal timeframe for this EA.");
    }
    else if(currentTF < PERIOD_H1)
    {
        Print("⚠ Lower timeframe detected.");
        Print("  Expect: MORE frequent signals, HIGHER noise");
        Print("  Adjust: Use tighter stops, smaller position sizes");
        Print("  Risk: More false signals, higher transaction costs");
    }
    else
    {
        Print("⚠ Higher timeframe detected.");
        Print("  Expect: FEWER signals, STRONGER trends");
        Print("  Adjust: Use wider stops, potentially larger positions");
        Print("  Benefit: Less noise, stronger signals");
    }
    
    // Calculate timeframe multiplier
    int currentMinutes = PeriodSeconds(currentTF) / 60;
    int h1Minutes = PeriodSeconds(PERIOD_H1) / 60;
    double multiplier = (double)currentMinutes / h1Minutes;
    
    Print("Timeframe multiplier vs H1: ", DoubleToString(multiplier, 2), "x");
    
    if(multiplier < 1.0)
    {
        Print("Suggested adjustments:");
        Print("- Reduce StopLossPips by ", DoubleToString((1.0 - multiplier) * 100, 0), "%");
        Print("- Reduce TakeProfitPips by ", DoubleToString((1.0 - multiplier) * 100, 0), "%");
        Print("- Consider reducing RiskPercentage");
    }
    else if(multiplier > 1.0)
    {
        Print("Suggested adjustments:");
        Print("- Increase StopLossPips by ", DoubleToString((multiplier - 1.0) * 100, 0), "%");
        Print("- Increase TakeProfitPips by ", DoubleToString((multiplier - 1.0) * 100, 0), "%");
        Print("- Consider increasing RiskPercentage slightly");
    }
}
