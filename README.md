# GoldMaster EA - Professional Gold Trading System

## 🏆 Overview

GoldMaster EA is a comprehensive, research-based Expert Advisor for MetaTrader 5, specifically designed for trading Gold (XAU/USD). This EA combines multiple proven strategies identified through extensive research of successful gold trading systems, academic papers, and community-validated approaches.

## ✨ Key Features

### 🎯 Multi-Strategy Approach
- **Open Range Breakout (ORB)**: Captures early momentum moves after market open
- **Trend Following**: EMA crossovers with RSI confirmation for major trends
- **Mean Reversion**: RSI-based signals for market corrections

### 🛡️ Advanced Risk Management
- Dynamic position sizing based on account equity
- Maximum drawdown protection (configurable)
- Intelligent stop loss and take profit calculation
- Trailing stop functionality
- Daily trade limits and time filters

### 📊 Gold-Specific Optimizations
- Optimized for XAU/USD volatility characteristics
- Market hours awareness
- Spread-adjusted profit targets
- Volatility-based position sizing

## 📁 Project Files

| File | Description |
|------|-------------|
| `GoldMaster_EA.mq5` | Main Expert Advisor file |
| `GoldMaster_EA_Test.mq5` | Comprehensive test suite |
| `GoldMaster_EA_Documentation.md` | Complete documentation |
| `README.md` | This overview file |

## 🚀 Quick Start

### Prerequisites
- MetaTrader 5 platform
- Gold (XAU/USD) trading account
- Minimum $1,000 account balance (recommended: $5,000+)

### Installation (5 Minutes)
1. Copy `GoldMaster_EA.mq5` to `MQL5\Experts\` folder
2. Copy `GoldMaster_EA_Test.mq5` to `MQL5\Scripts\` folder
3. Compile both files in MetaEditor (F7)
4. Run test script to verify installation
5. Attach EA to XAU/USD H1 chart

### Recommended Settings
```
Risk Percentage: 1.0%
Max Drawdown: 10.0%
Stop Loss: 400 pips
Take Profit: 1200 pips
Enable ORB: true
Enable Trend Following: true
Max Trades Per Day: 2
```

## 📈 Strategy Details

### Open Range Breakout (ORB)
Based on the proven GOLD_ORB strategy from research:
- Identifies opening range after market opens (1:02 server time)
- Waits for consolidation period (default 3 candles)
- Generates signals on range breakout/breakdown
- Excellent for capturing early momentum

### Trend Following
Uses moving average crossovers with confirmation:
- 50 EMA and 200 EMA crossovers
- RSI confirmation to filter false signals
- Enters on momentum continuation
- Good for trending market conditions

### Mean Reversion
RSI-based counter-trend strategy:
- Identifies overbought (>70) and oversold (<30) conditions
- Enters counter-trend positions
- Suitable for ranging markets
- Good risk-reward ratios

## 🔧 Configuration

### Risk Management Parameters
- `RiskPercentage`: 0.5% - 2.0% (default: 1.0%)
- `MaxDrawdownPercent`: 5% - 15% (default: 10.0%)
- `StopLossPips`: 300 - 500 (default: 400)
- `TakeProfitPips`: 900 - 1500 (default: 1200)

### Strategy Parameters
- `EnableORB`: Enable Open Range Breakout
- `EnableTrendFollowing`: Enable Trend Following
- `EnableMeanReversion`: Enable Mean Reversion
- `ORB_Period`: Consolidation period (default: 3)
- `MA_Fast/MA_Slow`: Moving average periods (50/200)

### Time Filters
- `StartTradingHour`: Start hour (default: 1)
- `StopTradingHour`: Stop hour (default: 23)
- `TradeOnMonday/Friday`: Day filters

## 📊 Performance Expectations

### Target Metrics
- **Profit Factor**: > 1.3
- **Win Rate**: > 45%
- **Maximum Drawdown**: < 15%
- **Sharpe Ratio**: > 1.0
- **Average Risk-Reward**: 1:3

### Recommended Broker Features
- Low spreads on XAU/USD (< 3 pips)
- Fast execution (< 100ms)
- No requotes
- Hedging allowed
- ECN or Standard account

## 🧪 Testing

The EA includes a comprehensive test suite (`GoldMaster_EA_Test.mq5`) that validates:
- Position sizing calculations
- Risk management functions
- Indicator operations
- Time filter logic
- Broker compatibility

Run the test script before live trading to ensure everything works correctly.

## 📚 Research Foundation

This EA is based on extensive research including:

### Academic Sources
- Gold trading volatility analysis
- Technical indicator effectiveness studies
- Risk management best practices
- Market microstructure research

### Community Sources
- **GOLD_ORB**: Open Range Breakout strategy (GitHub: yulz008/GOLD_ORB)
- **EA31337**: Multi-strategy framework approach
- **MQL5 Community**: Risk management techniques
- **Trading Forums**: Real-world validation

### Technical Indicators Research
- Moving Average effectiveness for gold
- RSI optimization for precious metals
- MACD trend confirmation studies
- ATR-based position sizing

## ⚠️ Risk Disclaimer

**IMPORTANT**: Trading involves significant risk and may not be suitable for all investors. Key considerations:

- Past performance does not guarantee future results
- Automated systems can fail due to technical issues
- Market conditions change and affect strategy performance
- Always test on demo accounts first
- Use only risk capital you can afford to lose
- Monitor performance regularly

## 🔄 Version History

- **v1.00**: Initial release with multi-strategy approach
  - Open Range Breakout implementation
  - Trend Following with EMA/RSI
  - Mean Reversion strategy
  - Comprehensive risk management
  - Advanced position sizing
  - Time filters and trade limits

## 📞 Support

### Getting Help
1. Review the complete documentation (`GoldMaster_EA_Documentation.md`)
2. Run the test suite to identify issues
3. Check MT5 Expert tab for error messages
4. Verify broker compatibility
5. Test on demo account first

### Common Issues
- **EA not trading**: Check AutoTrading enabled, sufficient margin, time filters
- **Poor performance**: Review spread conditions, broker execution quality
- **Compilation errors**: Ensure MT5 is updated, check syntax

## 📄 License

This Expert Advisor is provided for educational and research purposes. Users are responsible for:
- Testing thoroughly before live trading
- Understanding the risks involved
- Complying with local regulations
- Using appropriate risk management

---

**Developed**: January 2025  
**Platform**: MetaTrader 5  
**Optimized for**: XAU/USD (Gold)  
**Build Compatibility**: MT5 3815+

## 🎯 Next Steps

1. **Download** all project files
2. **Install** in MetaTrader 5
3. **Test** using the provided test suite
4. **Backtest** on historical data
5. **Demo trade** for at least 1 month
6. **Go live** with conservative settings
7. **Monitor** and optimize performance

Happy Trading! 📈✨
