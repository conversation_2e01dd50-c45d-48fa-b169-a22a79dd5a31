//+------------------------------------------------------------------+
//|                                   GoldMaster_EA_Fix_Test.mq5 |
//|                          Test script for the fixed calculations |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "GoldMaster EA Fix Test"
#property link      ""
#property version   "1.00"
#property script_show_inputs

//--- Include necessary libraries
#include <Trade\SymbolInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input parameters
input double TestPrice = 2000.0;           // Test price for calculations
input int TestStopLossPips = 400;          // Test stop loss in pips
input int TestTakeProfitPips = 1200;       // Test take profit in pips
input double TestRiskPercent = 1.0;        // Test risk percentage

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== GoldMaster EA Fix Test Started ===");
    
    CSymbolInfo symbolInfo;
    CAccountInfo accountInfo;
    
    if(!symbolInfo.Name(_Symbol))
    {
        Print("Error: Cannot initialize symbol info for ", _Symbol);
        return;
    }
    
    Print("\n--- Symbol Information ---");
    Print("Symbol: ", _Symbol);
    Print("Digits: ", symbolInfo.Digits());
    Print("Point: ", symbolInfo.Point());
    Print("Tick Size: ", symbolInfo.TickSize());
    Print("Tick Value: ", symbolInfo.TickValue());
    Print("Stops Level: ", symbolInfo.StopsLevel());
    Print("Min Lot: ", symbolInfo.LotsMin());
    Print("Max Lot: ", symbolInfo.LotsMax());
    Print("Lot Step: ", symbolInfo.LotsStep());
    
    // Test the fixed calculations
    TestStopLossCalculations(symbolInfo);
    TestPositionSizing(symbolInfo, accountInfo);
    
    Print("\n=== Fix Test Completed ===");
}

//+------------------------------------------------------------------+
//| Test stop loss and take profit calculations                      |
//+------------------------------------------------------------------+
void TestStopLossCalculations(CSymbolInfo &symbolInfo)
{
    Print("\n--- Testing Stop Loss/Take Profit Calculations ---");
    
    double point = symbolInfo.Point();
    double pipValue = point * 10; // For gold: 1 pip = 0.1
    
    Print("Point: ", point, " | Pip Value: ", pipValue);
    
    // Test BUY order
    double buyPrice = TestPrice;
    double buyStopLoss = buyPrice - (TestStopLossPips * pipValue);
    double buyTakeProfit = buyPrice + (TestTakeProfitPips * pipValue);
    
    Print("\n--- BUY Order Test ---");
    Print("Entry Price: ", buyPrice);
    Print("Stop Loss: ", buyStopLoss, " (Distance: ", (buyPrice - buyStopLoss), ")");
    Print("Take Profit: ", buyTakeProfit, " (Distance: ", (buyTakeProfit - buyPrice), ")");
    
    // Validate BUY stops
    double minStopLevel = symbolInfo.StopsLevel() * symbolInfo.Point();
    bool buyStopsValid = true;
    
    if(buyStopLoss >= buyPrice || buyTakeProfit <= buyPrice)
    {
        buyStopsValid = false;
        Print("ERROR: Invalid stop direction for BUY order");
    }
    
    if(buyPrice - buyStopLoss < minStopLevel || buyTakeProfit - buyPrice < minStopLevel)
    {
        buyStopsValid = false;
        Print("ERROR: Stops too close to price. Min level: ", minStopLevel);
    }
    
    Print("BUY Stops Valid: ", (buyStopsValid ? "YES" : "NO"));
    
    // Test SELL order
    double sellPrice = TestPrice;
    double sellStopLoss = sellPrice + (TestStopLossPips * pipValue);
    double sellTakeProfit = sellPrice - (TestTakeProfitPips * pipValue);
    
    Print("\n--- SELL Order Test ---");
    Print("Entry Price: ", sellPrice);
    Print("Stop Loss: ", sellStopLoss, " (Distance: ", (sellStopLoss - sellPrice), ")");
    Print("Take Profit: ", sellTakeProfit, " (Distance: ", (sellPrice - sellTakeProfit), ")");
    
    // Validate SELL stops
    bool sellStopsValid = true;
    
    if(sellStopLoss <= sellPrice || sellTakeProfit >= sellPrice)
    {
        sellStopsValid = false;
        Print("ERROR: Invalid stop direction for SELL order");
    }
    
    if(sellStopLoss - sellPrice < minStopLevel || sellPrice - sellTakeProfit < minStopLevel)
    {
        sellStopsValid = false;
        Print("ERROR: Stops too close to price. Min level: ", minStopLevel);
    }
    
    Print("SELL Stops Valid: ", (sellStopsValid ? "YES" : "NO"));
    
    // Summary
    if(buyStopsValid && sellStopsValid)
    {
        Print("\n✓ STOP LOSS CALCULATIONS: FIXED AND WORKING");
    }
    else
    {
        Print("\n✗ STOP LOSS CALCULATIONS: STILL HAVE ISSUES");
    }
}

//+------------------------------------------------------------------+
//| Test position sizing calculations                                |
//+------------------------------------------------------------------+
void TestPositionSizing(CSymbolInfo &symbolInfo, CAccountInfo &accountInfo)
{
    Print("\n--- Testing Position Sizing ---");
    
    double accountBalance = accountInfo.Balance();
    if(accountBalance <= 0)
        accountBalance = 10000.0; // Use test balance if no real balance
    
    double riskAmount = accountBalance * (TestRiskPercent / 100.0);
    
    Print("Account Balance: ", accountBalance);
    Print("Risk Amount: ", riskAmount, " (", TestRiskPercent, "%)");
    
    // Calculate pip value for gold
    double tickValue = symbolInfo.TickValue();
    double tickSize = symbolInfo.TickSize();
    double pipValue = tickValue * 10; // For gold: 1 pip = 10 ticks (if tick size is 0.01)
    
    Print("Tick Value: ", tickValue);
    Print("Tick Size: ", tickSize);
    Print("Pip Value: ", pipValue);
    
    // Calculate lot size
    double stopLossInPips = TestStopLossPips;
    double lotSize = riskAmount / (stopLossInPips * pipValue);
    
    Print("Stop Loss Pips: ", stopLossInPips);
    Print("Raw Lot Size: ", lotSize);
    
    // Apply lot constraints
    double minLot = symbolInfo.LotsMin();
    double maxLot = symbolInfo.LotsMax();
    double lotStep = symbolInfo.LotsStep();
    
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    
    if(lotStep > 0)
        lotSize = NormalizeDouble(MathRound(lotSize / lotStep) * lotStep, 2);
    
    Print("Final Lot Size: ", lotSize);
    Print("Lot Constraints: Min=", minLot, " Max=", maxLot, " Step=", lotStep);
    
    // Calculate actual risk with final lot size
    double actualRisk = lotSize * stopLossInPips * pipValue;
    double actualRiskPercent = (actualRisk / accountBalance) * 100;
    
    Print("Actual Risk: ", actualRisk, " (", actualRiskPercent, "%)");
    
    // Validation
    bool positionSizingValid = true;
    
    if(lotSize <= 0 || lotSize < minLot)
    {
        positionSizingValid = false;
        Print("ERROR: Invalid lot size");
    }
    
    if(actualRiskPercent > TestRiskPercent * 1.5) // Allow 50% tolerance
    {
        positionSizingValid = false;
        Print("ERROR: Actual risk too high");
    }
    
    if(positionSizingValid)
    {
        Print("\n✓ POSITION SIZING: FIXED AND WORKING");
    }
    else
    {
        Print("\n✗ POSITION SIZING: STILL HAVE ISSUES");
    }
}

//+------------------------------------------------------------------+
//| Test complete trade scenario                                     |
//+------------------------------------------------------------------+
void TestCompleteTradeScenario()
{
    Print("\n--- Complete Trade Scenario Test ---");
    
    CSymbolInfo symbolInfo;
    if(!symbolInfo.Name(_Symbol))
    {
        Print("Cannot test complete scenario - symbol info failed");
        return;
    }
    
    // Simulate a BUY trade
    double price = symbolInfo.Ask();
    if(price <= 0)
        price = TestPrice;
    
    Print("Simulating BUY trade at price: ", price);
    
    // Calculate stops using fixed method
    double point = symbolInfo.Point();
    double pipValue = point * 10;
    
    double stopLoss = price - (TestStopLossPips * pipValue);
    double takeProfit = price + (TestTakeProfitPips * pipValue);
    
    // Validate
    double minStopLevel = symbolInfo.StopsLevel() * symbolInfo.Point();
    
    bool valid = true;
    if(stopLoss >= price || takeProfit <= price)
        valid = false;
    if(price - stopLoss < minStopLevel || takeProfit - price < minStopLevel)
        valid = false;
    
    Print("Trade Parameters:");
    Print("  Price: ", price);
    Print("  Stop Loss: ", stopLoss);
    Print("  Take Profit: ", takeProfit);
    Print("  Min Stop Level: ", minStopLevel);
    Print("  Valid: ", (valid ? "YES" : "NO"));
    
    if(valid)
    {
        Print("\n✓ COMPLETE TRADE SCENARIO: READY FOR LIVE TRADING");
    }
    else
    {
        Print("\n✗ COMPLETE TRADE SCENARIO: NEEDS MORE FIXES");
    }
}
